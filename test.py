# -*- coding: utf-8 -*-
"""
Tushare Pro 免费用户 选股脚本（5 日涨幅 Top N），仅使用 daily 接口
消耗积分：⚠️ 不产生积分扣减，仅需最低120积分门槛。若运行多日历史数据，
请注意每分钟调用次数限额为 500 次（free 页使用者）
详细说明见：https://tushare.pro/document/2?doc_id=27
"""

import os
import time
import pandas as pd
from datetime import datetime, timedelta
import tushare as ts

# ⚠️ 请设置你的 Tushare Token
ts.set_token(os.environ.get("TUSHARE_TOKEN", "your_token_here"))
pro = ts.pro_api()

def fetch_daily_for_dates(dates):
    """
    批量调用 daily 接口，每个日期调一次
    输入: dates -- ['20250728', '20250729', ...]
    返回: dict {date_str: DataFrame}
    """
    dfs = {}
    for dt in dates:
        print(f"Fetching daily for trade_date={dt} …")
        df = pro.daily(trade_date=dt,
                       fields="ts_code,trade_date,pct_chg,vol,amount")
        dfs[dt] = df
        time.sleep(0.6)  # 避免频率限制（500 次/min）
    return dfs

def select_top_momentum(end_date: str, lookback: int = 5, top_n: int = 20,
                        min_amt_cny: float = 1e8):
    """
    end_date: str, e.g. '20250728'
    lookback: lookback trading days (including end_date)
    top_n: select top N
    min_amt_cny: min daily成交额门槛（元）
    """
    dates = [(datetime.strptime(end_date, "%Y%m%d") - timedelta(days=i)) for i in range(lookback)]
    # 💡 若遇到周末/节假日，可手动跳过
    dates = [d.strftime("%Y%m%d") for d in sorted(dates)]
    data_map = fetch_daily_for_dates(dates)
    # 确保每日入市样本一致
    idx0 = set(data_map[dates[0]]["ts_code"])
    for dt in dates[1:]:
        idx0 &= set(data_map[dt]["ts_code"])
    idx0 = sorted(idx0)
    print(f"共有 {len(idx0)} 支股票连续 {lookback} 个交易日有记录（剔除停牌）")

    # 构造累计涨幅
    df_long = []
    for dt in dates:
        tmp = data_map[dt][["ts_code", "pct_chg", "amount"]].copy()
        tmp.columns = ["ts_code", f"chg_{dt}", f"amt_{dt}"]
        df_long.append(tmp.set_index("ts_code"))
    df_comb = pd.concat(df_long, axis=1)
    df_comb = df_comb.loc[idx0]

    # 成交额过滤：每日都大于 min_amt 门槛
    amt_cols = [c for c in df_comb.columns if c.startswith("amt_")]
    df_comb = df_comb[(df_comb[amt_cols] >= (min_amt_cny / 1000)).all(axis=1)]

    # 累计涨幅计算：(1 + pct1)*(1+pct2)... -1
    chg_cols = [c for c in df_comb.columns if c.startswith("chg_")]
    df_comb["cum_return"] = (df_comb[chg_cols].div(100).add(1).prod(axis=1) - 1)
    df_result = df_comb["cum_return"].sort_values(ascending=False).iloc[:top_n]

    return df_result

if __name__ == "__main__":
    # 用户自行指定作为样本的交易日
    TRADE_DATE = "20250728"  # 请替换为实际最新交易日
    results = select_top_momentum(TRADE_DATE, lookback=5, top_n=10,
                                  min_amt_cny=200e6)
    print(f"\n过去 5 个交易日累计涨幅 Top 10（剔除成交额 <2 亿元）:")
    for ts_code, ret in results.iteritems():
        print(f"{ts_code}: {ret*100:.2f}%")
